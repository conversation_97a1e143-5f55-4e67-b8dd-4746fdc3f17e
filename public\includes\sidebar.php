<?php
// Determine the correct path to assets based on current directory
$current_dir = dirname($_SERVER['SCRIPT_NAME']);
$base_path = (basename($current_dir) === 'public') ? './' : '../';
?>
<!-- Sidebar -->
<aside id="logo-sidebar" class="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0 dark:bg-gray-800 dark:border-gray-700" aria-label="Sidebar">
    <div class="h-full px-3 pb-4 overflow-y-auto bg-white dark:bg-gray-800">
        <ul class="space-y-2 font-medium">
            <!-- Dashboard -->
            <li>
                <a href="<?php echo $base_path; ?>dashboard.php" class="sidebar-link group <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Dashboard</span>
                </a>
            </li>

            <!-- Patient Registration -->
            <li>
                <a href="<?php echo $base_path; ?>patients/add.php" class="sidebar-link group <?php echo (strpos($_SERVER['PHP_SELF'], 'patients') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-user-plus w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Patient Registration</span>
                </a>
            </li>

            <!-- Staff Registration -->
            <li>
                <a href="<?php echo $base_path; ?>staff/add.php" class="sidebar-link group <?php echo (strpos($_SERVER['PHP_SELF'], 'staff') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-user-md w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Staff Registration</span>
                </a>
            </li>

            <!-- Appointment Scheduling -->
            <li>
                <a href="<?php echo $base_path; ?>appointments/add.php" class="sidebar-link group <?php echo (strpos($_SERVER['PHP_SELF'], 'appointments') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-plus w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Appointment Scheduling</span>
                </a>
            </li>

            <!-- Medical Record Entry -->
            <li>
                <a href="<?php echo $base_path; ?>medical-records/add.php" class="sidebar-link group <?php echo (strpos($_SERVER['PHP_SELF'], 'medical-records') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-file-medical w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Medical Record Entry</span>
                </a>
            </li>

            <!-- Bed Assignment -->
            <li>
                <a href="<?php echo $base_path; ?>beds/add.php" class="sidebar-link group <?php echo (strpos($_SERVER['PHP_SELF'], 'beds') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-bed w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Bed Assignment</span>
                </a>
            </li>
            
            <!-- Divider -->
            <li class="pt-4 mt-4 space-y-2 border-t border-gray-200 dark:border-gray-700">
                <span class="text-xs font-medium text-gray-500 uppercase dark:text-gray-400">Records & Reports</span>
            </li>
            




            <!-- Appointment Calendar -->
            <li>
                <a href="<?php echo $base_path; ?>appointments/calendar.php" class="sidebar-link group">
                    <i class="fas fa-calendar-alt w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Appointment Calendar</span>
                </a>
            </li>

            <!-- Medical History -->
            <li>
                <a href="<?php echo $base_path; ?>medical-records/list.php" class="sidebar-link group">
                    <i class="fas fa-history w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Medical History</span>
                </a>
            </li>

            <!-- Ward Status -->
            <li>
                <a href="<?php echo $base_path; ?>beds/status.php" class="sidebar-link group">
                    <i class="fas fa-hospital w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                    <span class="ms-3">Ward Status</span>
                </a>
            </li>
        </ul>
    </div>
</aside>
