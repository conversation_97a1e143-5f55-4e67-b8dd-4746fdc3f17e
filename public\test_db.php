<?php
// Test database connection and table structure
$username = 'root';
$password = '';
$database = 'edlivkyhospital';
$server = '127.0.0.1';

echo "<h2>Testing Database Connection</h2>";

$db_handle = mysqli_connect($server, $username, $password);  
$db_found = mysqli_select_db($db_handle, $database);        

if ($db_found) {
    echo "✅ Database Found: $database<br><br>";
    
    // Check if patients table exists
    $table_check = "SHOW TABLES LIKE 'patients'";
    $result = mysqli_query($db_handle, $table_check);
    
    if (mysqli_num_rows($result) > 0) {
        echo "✅ Patients table exists<br><br>";
        
        // Show table structure
        echo "<h3>Patients Table Structure:</h3>";
        $structure_query = "DESCRIBE patients";
        $structure_result = mysqli_query($db_handle, $structure_query);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = mysqli_fetch_array($structure_result)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
        // Show existing data
        echo "<h3>Existing Patient Data:</h3>";
        $data_query = "SELECT * FROM patients LIMIT 5";
        $data_result = mysqli_query($db_handle, $data_query);
        
        if (mysqli_num_rows($data_result) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            
            // Get column names
            $fields = mysqli_fetch_fields($data_result);
            echo "<tr>";
            foreach ($fields as $field) {
                echo "<th>" . $field->name . "</th>";
            }
            echo "</tr>";
            
            // Show data
            while ($row = mysqli_fetch_array($data_result)) {
                echo "<tr>";
                foreach ($fields as $field) {
                    echo "<td>" . ($row[$field->name] ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No patient data found.";
        }
        
    } else {
        echo "❌ Patients table does not exist<br>";
    }
    
} else {
    echo "❌ Database NOT Found: $database<br>";
    echo "Error: " . mysqli_connect_error();
}   

mysqli_close($db_handle);
?>
